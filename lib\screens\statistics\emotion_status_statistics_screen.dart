import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../widgets/time_period_selector.dart';
import '../../widgets/chart_view_selector.dart';
import '../../services/mock_data_service.dart';
import '../../models/extended_health_data.dart';
import '../../models/emotion_status.dart';

/// 情绪状态统计界面
class EmotionStatusStatisticsScreen extends StatefulWidget {
  @override
  _EmotionStatusStatisticsScreenState createState() => _EmotionStatusStatisticsScreenState();
}

class _EmotionStatusStatisticsScreenState extends State<EmotionStatusStatisticsScreen> {
  TimePeriod _selectedPeriod = TimePeriod.day;
  ChartViewType _selectedChartType = ChartViewType.line;
  DateTime _currentDate = DateTime.now();
  List<ExtendedHealthData> _healthData = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// 加载数据
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      List<ExtendedHealthData> data;
      switch (_selectedPeriod) {
        case TimePeriod.day:
          data = MockDataService.generateDayData(date: _currentDate);
          break;
        case TimePeriod.week:
          data = MockDataService.generateWeekData(startDate: _currentDate);
          break;
        case TimePeriod.month:
          data = MockDataService.generateMonthData(month: _currentDate);
          break;
      }
      
      setState(() {
        _healthData = data;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('加载数据失败: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('情绪状态统计'),
        backgroundColor: Colors.purple.shade50,
      ),
      body: Column(
        children: [
          // 时间段选择器
          TimePeriodSelectorWithNavigation(
            selectedPeriod: _selectedPeriod,
            onPeriodChanged: (period) {
              setState(() {
                _selectedPeriod = period;
              });
              _loadData();
            },
            currentDate: _currentDate,
            onDateChanged: (date) {
              setState(() {
                _currentDate = date;
              });
              _loadData();
            },
          ),
          
          // 图表类型选择器
          ChartViewSelector(
            availableTypes: ChartTypePresets.emotionStatusStatistics,
            selectedType: _selectedChartType,
            onTypeChanged: (type) {
              setState(() {
                _selectedChartType = type;
              });
            },
          ),
          
          // 图表区域
          Expanded(
            child: _isLoading
                ? Center(child: CircularProgressIndicator())
                : Padding(
                    padding: EdgeInsets.all(16),
                    child: Card(
                      elevation: 4,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _selectedChartType == ChartViewType.line ? '情绪水平变化' : '情绪状态分布',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                            SizedBox(height: 16),
                            Expanded(
                              child: _buildChart(),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
          ),
          
          // 统计信息
          if (!_isLoading) _buildStatisticsInfo(),
        ],
      ),
    );
  }

  /// 构建图表
  Widget _buildChart() {
    if (_healthData.isEmpty) {
      return Center(
        child: Text(
          '暂无数据',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade600,
          ),
        ),
      );
    }

    switch (_selectedChartType) {
      case ChartViewType.line:
        return _buildLineChart();
      case ChartViewType.groupedBar:
        return _buildGroupedBarChart();
      default:
        return _buildLineChart();
    }
  }

  /// 构建曲线图（情绪水平变化）
  Widget _buildLineChart() {
    final spots = _healthData.asMap().entries.map((entry) {
      return FlSpot(entry.key.toDouble(), entry.value.emotionStatus.emotionLevel.toDouble());
    }).toList();

    return AspectRatio(
      aspectRatio: 2.0, // 设置宽高比为2:1
      child: LineChart(
        LineChartData(
          gridData: FlGridData(
            show: true,
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color: Colors.grey.shade300,
                strokeWidth: 1,
                dashArray: [5, 5], // 设置虚线样式
              );
            },
          ),
          titlesData: FlTitlesData(
            leftTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)), // 隐藏左侧标题
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 40, // 增加底部预留空间，拉开与绘图区域的距离
                interval: _getBottomTitleInterval(), // 根据不同模式设置不同间隔
                getTitlesWidget: (value, meta) {
                  return Padding(
                    padding: const EdgeInsets.only(top: 8.0), // 向下推进横坐标值
                    child: Text(
                      _getBottomTitle(value.toInt()),
                      style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                    ),
                  );
                },
              ),
            ),
            rightTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 40, // 减少右侧预留空间，让数值更靠近右边缘
                getTitlesWidget: (value, meta) {
                  return Padding(
                    padding: const EdgeInsets.only(left: 8.0), // 向右推进数值
                    child: Text(
                      '${value.toInt()}',
                      style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                      textAlign: TextAlign.right,
                    ),
                  );
                },
              ),
            ), // 显示右侧情绪等级标题
            topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          ),
          borderData: FlBorderData(show: false), // 去掉坐标轴外框
          lineTouchData: LineTouchData(
            enabled: true,
            touchTooltipData: LineTouchTooltipData(
              getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
                return touchedBarSpots.map((barSpot) {
                  final index = barSpot.x.toInt();
                  String timeRange = '';

                  if (_selectedPeriod == TimePeriod.day) {
                    // 日模式显示时间段
                    final hour = index ~/ 4;
                    final minute = (index % 4) * 15;
                    final nextMinute = minute + 15;
                    if (nextMinute == 60) {
                      timeRange = '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}~${(hour + 1).toString().padLeft(2, '0')}:00';
                    } else {
                      timeRange = '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}~${hour.toString().padLeft(2, '0')}:${nextMinute.toString().padLeft(2, '0')}';
                    }
                  } else if (_selectedPeriod == TimePeriod.month) {
                    // 月模式显示具体日期
                    final day = index + 1;
                    final month = DateTime.now().month; // 使用当前月份
                    timeRange = '${month}月${day}日';
                  } else {
                    // 周模式显示星期
                    final weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
                    timeRange = weekdays[index % 7];
                  }

                  return LineTooltipItem(
                    '$timeRange\n情绪等级${barSpot.y.toInt()}',
                    const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  );
                }).toList();
              },
            ),
          ),
          lineBarsData: [
            LineChartBarData(
              spots: spots,
              isCurved: true, // 使用光滑曲线
              curveSmoothness: 0.35, // 设置曲线平滑度，值越大越平滑
              color: Colors.purple,
              barWidth: 3,
              dotData: FlDotData(show: false), // 不显示曲线上的小圆点
              belowBarData: BarAreaData(
                show: true,
                color: Colors.purple.withOpacity(0.1),
              ),
            ),
          ],
        // 添加情绪水平参考线
        extraLinesData: ExtraLinesData(
          horizontalLines: [
            HorizontalLine(
              y: 4.0, // 中性情绪线
              color: Colors.grey.withOpacity(0.5),
              strokeWidth: 2,
              dashArray: [5, 5],
            ),
          ],
        ),
      ),
    ),
    );
  }

  /// 构建簇状柱形图（正面/负面情绪分布）
  Widget _buildGroupedBarChart() {
    // 按时间段分组统计正面和负面情绪
    final Map<int, Map<String, int>> groupedData = {};
    
    for (int i = 0; i < _healthData.length; i++) {
      final data = _healthData[i];
      final groupIndex = _getGroupIndex(i);
      
      if (!groupedData.containsKey(groupIndex)) {
        groupedData[groupIndex] = {'positive': 0, 'negative': 0};
      }
      
      if (data.emotionStatus.isPositive) {
        groupedData[groupIndex]!['positive'] = groupedData[groupIndex]!['positive']! + 1;
      } else {
        groupedData[groupIndex]!['negative'] = groupedData[groupIndex]!['negative']! + 1;
      }
    }

    final barGroups = groupedData.entries.map((entry) {
      return BarChartGroupData(
        x: entry.key,
        barRods: [
          BarChartRodData(
            toY: entry.value['positive']!.toDouble(),
            color: Colors.green,
            width: 12,
            borderRadius: BorderRadius.circular(2),
          ),
          BarChartRodData(
            toY: entry.value['negative']!.toDouble(),
            color: Colors.red,
            width: 12,
            borderRadius: BorderRadius.circular(2),
          ),
        ],
      );
    }).toList();

    return Column(
      children: [
        // 图例
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildLegendItem('正面情绪', Colors.green),
            SizedBox(width: 20),
            _buildLegendItem('负面情绪', Colors.red),
          ],
        ),
        SizedBox(height: 16),
        Expanded(
          child: AspectRatio(
            aspectRatio: 2.0, // 设置宽高比为2:1
            child: BarChart(
              BarChartData(
                gridData: FlGridData(
                  show: true,
                  getDrawingHorizontalLine: (value) {
                    return FlLine(
                      color: Colors.grey.shade300,
                      strokeWidth: 1,
                      dashArray: [5, 5], // 设置虚线样式
                    );
                  },
                ),
                titlesData: FlTitlesData(
                  leftTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)), // 隐藏左侧标题
                  rightTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 40, // 减少右侧预留空间，让数值更靠近右边缘
                      getTitlesWidget: (value, meta) {
                        return Padding(
                          padding: const EdgeInsets.only(left: 8.0), // 向右推进数值
                          child: Text(
                            value.toInt().toString(),
                            style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                            textAlign: TextAlign.right,
                          ),
                        );
                      },
                    ),
                  ), // 显示右侧数值标题
                bottomTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    reservedSize: 40, // 增加底部预留空间，拉开与绘图区域的距离
                    getTitlesWidget: (value, meta) {
                      return Padding(
                        padding: const EdgeInsets.only(top: 8.0), // 向下推进横坐标值
                        child: Text(
                          _getGroupTitle(value.toInt()),
                          style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                        ),
                      );
                    },
                  ),
                ),
                topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
              ),
              borderData: FlBorderData(show: false), // 去掉坐标轴外框
              barTouchData: BarTouchData(
                enabled: true,
                touchTooltipData: BarTouchTooltipData(
                  getTooltipItem: (group, groupIndex, rod, rodIndex) {
                    final groupTitle = _getGroupTitle(groupIndex);
                    final emotionType = rodIndex == 0 ? '正面情绪' : '负面情绪';
                    return BarTooltipItem(
                      '$groupTitle\n$emotionType: ${rod.toY.toInt()}次',
                      const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    );
                  },
                ),
              ),
              barGroups: barGroups,
              groupsSpace: 20,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建图例项
  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        SizedBox(width: 4),
        Text(
          label,
          style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
        ),
      ],
    );
  }

  /// 获取分组索引
  int _getGroupIndex(int dataIndex) {
    switch (_selectedPeriod) {
      case TimePeriod.day:
        return dataIndex ~/ 4; // 每4个数据点为一组（1小时）
      case TimePeriod.week:
        return dataIndex; // 每天一组
      case TimePeriod.month:
        return dataIndex ~/ 7; // 每周一组
    }
  }

  /// 获取分组标题
  String _getGroupTitle(int groupIndex) {
    switch (_selectedPeriod) {
      case TimePeriod.day:
        final hour = groupIndex * 1; // 每小时
        return '${hour}时';
      case TimePeriod.week:
        final date = _currentDate.subtract(Duration(days: 6 - groupIndex));
        return '${date.month}/${date.day}';
      case TimePeriod.month:
        final weekNumber = groupIndex + 1;
        return '第${weekNumber}周';
    }
  }

  /// 获取底部标题间隔
  double _getBottomTitleInterval() {
    switch (_selectedPeriod) {
      case TimePeriod.day:
        return 12.0; // 日模式：3小时间隔(12个15分钟)
      case TimePeriod.week:
        return 1.0; // 周模式：每天显示
      case TimePeriod.month:
        return 5.0; // 月模式：每5天显示一个标签
    }
  }

  /// 获取底部标题
  String _getBottomTitle(int index) {
    if (index >= _healthData.length) return '';
    
    final data = _healthData[index];
    switch (_selectedPeriod) {
      case TimePeriod.day:
        return '${data.datetime.hour}:${data.datetime.minute.toString().padLeft(2, '0')}';
      case TimePeriod.week:
        return '${data.datetime.month}/${data.datetime.day}';
      case TimePeriod.month:
        return '${data.datetime.day}日';
    }
  }

  /// 构建统计信息
  Widget _buildStatisticsInfo() {
    final emotionLevels = _healthData.map((d) => d.emotionStatus.emotionLevel).toList();
    final avgLevel = emotionLevels.fold<int>(0, (sum, level) => sum + level) / emotionLevels.length;
    
    final positiveCount = _healthData.where((d) => d.emotionStatus.isPositive).length;
    final negativeCount = _healthData.length - positiveCount;
    
    final mostCommonEmotion = _getMostCommonEmotion();

    return Container(
      padding: EdgeInsets.all(16),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem('平均情绪', avgLevel.toStringAsFixed(1), Colors.purple),
                  _buildStatItem('正面情绪', '$positiveCount次', Colors.green),
                  _buildStatItem('负面情绪', '$negativeCount次', Colors.red),
                ],
              ),
              SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem('最常见', mostCommonEmotion.displayName, 
                      Color(int.parse('0xFF${mostCommonEmotion.colorHex.substring(1)}'))),
                  _buildStatItem('情绪稳定性', _getEmotionStability(), Colors.blue),
                  _buildStatItem('记录数', '${_healthData.length}', Colors.grey),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 获取最常见的情绪状态
  EmotionStatus _getMostCommonEmotion() {
    final emotionCounts = <EmotionStatus, int>{};
    for (final data in _healthData) {
      emotionCounts[data.emotionStatus] = (emotionCounts[data.emotionStatus] ?? 0) + 1;
    }
    return emotionCounts.entries.reduce((a, b) => a.value > b.value ? a : b).key;
  }

  /// 获取情绪稳定性描述
  String _getEmotionStability() {
    final levels = _healthData.map((d) => d.emotionStatus.emotionLevel).toList();
    if (levels.isEmpty) return '无数据';
    
    final variance = _calculateVariance(levels);
    if (variance < 1.0) return '很稳定';
    if (variance < 2.0) return '较稳定';
    if (variance < 3.0) return '一般';
    return '波动大';
  }

  /// 计算方差
  double _calculateVariance(List<int> values) {
    final mean = values.fold<int>(0, (sum, value) => sum + value) / values.length;
    final squaredDiffs = values.map((value) => (value - mean) * (value - mean));
    return squaredDiffs.fold<double>(0, (sum, diff) => sum + diff) / values.length;
  }

  /// 构建统计项
  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
        SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }
}
