import 'dart:math';
import '../models/extended_health_data.dart';
import '../models/activity_status.dart';
import '../models/emotion_status.dart';
import '../models/health_detection.dart';

/// Mock数据服务，用于生成测试数据
class MockDataService {
  static final Random _random = Random();

  /// 生成单个扩展健康数据
  static ExtendedHealthData generateSingleHealthData({DateTime? dateTime}) {
    final now = dateTime ?? DateTime.now();
    
    return ExtendedHealthData(
      running: _random.nextInt(50) + 10,
      walking: _random.nextInt(100) + 50,
      jumping: _random.nextInt(30) + 5,
      stepCount: _random.nextInt(5000) + 1000,
      temperature: 36.5 + _random.nextDouble() * 2.5, // 36.5-39.0度
      datetime: now,
      calories: _random.nextDouble() * 500 + 100, // 100-600卡路里
      activityStatus: ActivityStatus.values[_random.nextInt(ActivityStatus.values.length)],
      emotionStatus: EmotionStatus.values[_random.nextInt(EmotionStatus.values.length)],
      healthDetections: _generateRandomHealthDetections(),
    );
  }

  /// 生成随机健康检测结果
  static List<HealthDetection> _generateRandomHealthDetections() {
    final detectionCount = _random.nextInt(4); // 0-3个检测结果
    if (detectionCount == 0) {
      return []; // 正常情况
    }

    final allDetections = HealthDetection.values.toList();
    allDetections.shuffle(_random);
    return allDetections.take(detectionCount).toList();
  }

  /// 生成日视图数据（24小时，每15分钟一个数据点）
  static List<ExtendedHealthData> generateDayData({DateTime? date}) {
    final targetDate = date ?? DateTime.now();
    final startOfDay = DateTime(targetDate.year, targetDate.month, targetDate.day);
    final dataPoints = <ExtendedHealthData>[];
    int cumulativeSteps = 0; // 累加步数

    for (int i = 0; i < 96; i++) { // 24小时 * 4个15分钟间隔
      final time = startOfDay.add(Duration(minutes: i * 15));
      // 每15分钟增加一些步数（模拟真实情况）
      final stepIncrement = _random.nextInt(50) + 10; // 10-59步的增量
      cumulativeSteps += stepIncrement;

      final data = generateSingleHealthData(dateTime: time);
      // 使用累加的步数替换随机步数
      final updatedData = ExtendedHealthData(
        running: data.running,
        walking: data.walking,
        jumping: data.jumping,
        stepCount: cumulativeSteps,
        temperature: data.temperature,
        datetime: time,
        calories: data.calories,
        activityStatus: data.activityStatus,
        emotionStatus: data.emotionStatus,
        healthDetections: data.healthDetections,
      );
      dataPoints.add(updatedData);
    }

    return dataPoints;
  }

  /// 生成周视图数据（7天，每天一个数据点）
  static List<ExtendedHealthData> generateWeekData({DateTime? startDate}) {
    // 如果提供了startDate，使用它作为周的开始日期
    // 否则使用当前日期往前推6天作为周的开始
    final start = startDate != null
        ? DateTime(startDate.year, startDate.month, startDate.day).subtract(Duration(days: 6))
        : DateTime.now().subtract(Duration(days: 6));
    final dataPoints = <ExtendedHealthData>[];

    for (int i = 0; i < 7; i++) {
      final date = start.add(Duration(days: i));
      // 周模式使用随机步数，不累计
      final randomSteps = _random.nextInt(8000) + 2000; // 2000-9999步的随机值

      final data = generateSingleHealthData(dateTime: date);
      // 使用随机步数
      final updatedData = ExtendedHealthData(
        running: data.running,
        walking: data.walking,
        jumping: data.jumping,
        stepCount: randomSteps,
        temperature: data.temperature,
        datetime: date,
        calories: data.calories,
        activityStatus: data.activityStatus,
        emotionStatus: data.emotionStatus,
        healthDetections: data.healthDetections,
      );
      dataPoints.add(updatedData);
    }

    return dataPoints;
  }

  /// 生成月视图数据（当月每天一个数据点）
  static List<ExtendedHealthData> generateMonthData({DateTime? month}) {
    final targetMonth = month ?? DateTime.now();
    final endOfMonth = DateTime(targetMonth.year, targetMonth.month + 1, 0);
    final dataPoints = <ExtendedHealthData>[];

    for (int day = 1; day <= endOfMonth.day; day++) {
      final date = DateTime(targetMonth.year, targetMonth.month, day);
      // 月模式使用随机步数，不累计
      final randomSteps = _random.nextInt(8000) + 2000; // 2000-9999步的随机值

      final data = generateSingleHealthData(dateTime: date);
      // 使用随机步数
      final updatedData = ExtendedHealthData(
        running: data.running,
        walking: data.walking,
        jumping: data.jumping,
        stepCount: randomSteps,
        temperature: data.temperature,
        datetime: date,
        calories: data.calories,
        activityStatus: data.activityStatus,
        emotionStatus: data.emotionStatus,
        healthDetections: data.healthDetections,
      );
      dataPoints.add(updatedData);
    }

    return dataPoints;
  }

  /// 生成步数统计数据
  static Map<String, List<ExtendedHealthData>> generateStepStatistics() {
    return {
      'day': generateDayData(),
      'week': generateWeekData(),
      'month': generateMonthData(),
    };
  }

  /// 生成卡路里统计数据
  static Map<String, List<ExtendedHealthData>> generateCalorieStatistics() {
    return {
      'day': generateDayData(),
      'week': generateWeekData(),
      'month': generateMonthData(),
    };
  }

  /// 生成体温统计数据
  static Map<String, List<ExtendedHealthData>> generateTemperatureStatistics() {
    return {
      'day': generateDayData(),
      'week': generateWeekData(),
      'month': generateMonthData(),
    };
  }

  /// 生成活动状态统计数据
  static Map<String, List<ExtendedHealthData>> generateActivityStatusStatistics() {
    return {
      'day': generateDayData(),
      'week': generateWeekData(),
      'month': generateMonthData(),
    };
  }

  /// 生成情绪状态统计数据
  static Map<String, List<ExtendedHealthData>> generateEmotionStatusStatistics() {
    return {
      'day': generateDayData(),
      'week': generateWeekData(),
      'month': generateMonthData(),
    };
  }

  /// 生成健康检测统计数据
  static Map<String, List<ExtendedHealthData>> generateHealthDetectionStatistics() {
    return {
      'day': generateDayData(),
      'week': generateWeekData(),
      'month': generateMonthData(),
    };
  }

  /// 生成特定活动状态的数据（用于活动状态曲线图）
  static List<double> generateActivityStatusValues(
    List<ExtendedHealthData> data,
    ActivityStatus targetStatus,
  ) {
    return data.map((item) {
      // 如果当前数据点的活动状态匹配目标状态，返回强度等级，否则返回0
      return item.activityStatus == targetStatus ? targetStatus.intensityLevel.toDouble() : 0.0;
    }).toList();
  }

  /// 生成情绪状态统计计数
  static Map<EmotionStatus, int> generateEmotionStatusCounts(List<ExtendedHealthData> data) {
    final counts = <EmotionStatus, int>{};
    for (final status in EmotionStatus.values) {
      counts[status] = 0;
    }

    for (final item in data) {
      counts[item.emotionStatus] = (counts[item.emotionStatus] ?? 0) + 1;
    }

    return counts;
  }

  /// 生成健康检测统计计数
  static Map<HealthDetection, int> generateHealthDetectionCounts(List<ExtendedHealthData> data) {
    final counts = <HealthDetection, int>{};
    for (final detection in HealthDetection.values) {
      counts[detection] = 0;
    }

    for (final item in data) {
      for (final detection in item.healthDetections) {
        counts[detection] = (counts[detection] ?? 0) + 1;
      }
    }

    return counts;
  }
}
