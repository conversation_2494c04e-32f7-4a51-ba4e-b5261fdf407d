import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../widgets/time_period_selector.dart';
import '../../widgets/chart_view_selector.dart';
import '../../services/mock_data_service.dart';
import '../../models/extended_health_data.dart';
import '../../models/activity_status.dart';

/// 活动状态统计界面
class ActivityStatusStatisticsScreen extends StatefulWidget {
  @override
  _ActivityStatusStatisticsScreenState createState() => _ActivityStatusStatisticsScreenState();
}

class _ActivityStatusStatisticsScreenState extends State<ActivityStatusStatisticsScreen> {
  TimePeriod _selectedPeriod = TimePeriod.day;
  ChartViewType _selectedChartType = ChartViewType.bar;
  DateTime _currentDate = DateTime.now();
  List<ExtendedHealthData> _healthData = [];
  String? _selectedActivityStatus;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// 加载数据
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      List<ExtendedHealthData> data;
      switch (_selectedPeriod) {
        case TimePeriod.day:
          data = MockDataService.generateDayData(date: _currentDate);
          break;
        case TimePeriod.week:
          data = MockDataService.generateWeekData(startDate: _currentDate);
          break;
        case TimePeriod.month:
          data = MockDataService.generateMonthData(month: _currentDate);
          break;
      }
      
      setState(() {
        _healthData = data;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('加载数据失败: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('活动状态统计'),
        backgroundColor: Colors.green.shade50,
      ),
      body: Column(
        children: [
          // 时间段选择器
          TimePeriodSelectorWithNavigation(
            selectedPeriod: _selectedPeriod,
            onPeriodChanged: (period) {
              setState(() {
                _selectedPeriod = period;
              });
              _loadData();
            },
            currentDate: _currentDate,
            onDateChanged: (date) {
              setState(() {
                _currentDate = date;
              });
              _loadData();
            },
          ),
          
          // 图表类型和活动状态选择器
          CombinedChartSelector(
            availableChartTypes: ChartTypePresets.activityStatusStatistics,
            selectedChartType: _selectedChartType,
            onChartTypeChanged: (type) {
              setState(() {
                _selectedChartType = type;
              });
            },
            availableStatuses: ActivityStatus.values.map((s) => s.displayName).toList(),
            selectedStatus: _selectedActivityStatus,
            onStatusChanged: (status) {
              setState(() {
                _selectedActivityStatus = status;
              });
            },
          ),
          
          // 图表区域
          Expanded(
            child: _isLoading
                ? Center(child: CircularProgressIndicator())
                : Padding(
                    padding: EdgeInsets.all(16),
                    child: Card(
                      elevation: 4,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _selectedChartType == ChartViewType.line && _selectedActivityStatus != null
                                  ? '$_selectedActivityStatus 活动趋势'
                                  : '活动状态分布',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                            SizedBox(height: 16),
                            Expanded(
                              child: _buildChart(),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
          ),
          
          // 统计信息
          if (!_isLoading) _buildStatisticsInfo(),
        ],
      ),
    );
  }

  /// 构建图表
  Widget _buildChart() {
    if (_healthData.isEmpty) {
      return Center(
        child: Text(
          '暂无数据',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade600,
          ),
        ),
      );
    }

    switch (_selectedChartType) {
      case ChartViewType.line:
        return _buildLineChart();
      case ChartViewType.bar:
        return _buildBarChart();
      default:
        return _buildBarChart();
    }
  }

  /// 构建曲线图（特定活动状态的强度变化）
  Widget _buildLineChart() {
    if (_selectedActivityStatus == null) {
      return Center(
        child: Text(
          '请选择活动状态',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade600,
          ),
        ),
      );
    }

    final selectedStatus = ActivityStatus.values.firstWhere(
      (status) => status.displayName == _selectedActivityStatus,
    );

    final spots = _healthData.asMap().entries.where((entry) {
      return entry.value.activityStatus == selectedStatus;
    }).map((entry) {
      return FlSpot(entry.key.toDouble(), entry.value.activityStatus.intensityLevel.toDouble());
    }).toList();

    if (spots.isEmpty) {
      return Center(
        child: Text(
          '该时间段内无 $_selectedActivityStatus 活动',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade600,
          ),
        ),
      );
    }

    final color = Color(int.parse('0xFF${selectedStatus.colorHex.substring(1)}'));

    return LineChart(
      LineChartData(
        gridData: FlGridData(show: true),
        titlesData: FlTitlesData(
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text(
                  '强度${value.toInt()}',
                  style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                );
              },
            ),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              getTitlesWidget: (value, meta) {
                return Text(
                  _getBottomTitle(value.toInt()),
                  style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                );
              },
            ),
          ),
          rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: true),
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: true,
            color: color,
            barWidth: 3,
            dotData: FlDotData(show: true),
            belowBarData: BarAreaData(
              show: true,
              color: color.withOpacity(0.1),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建柱状图（活动状态分布）
  Widget _buildBarChart() {
    // 统计各活动状态的出现次数
    final statusCounts = <ActivityStatus, int>{};
    for (final status in ActivityStatus.values) {
      statusCounts[status] = _healthData.where((data) => data.activityStatus == status).length;
    }

    final barGroups = statusCounts.entries.map((entry) {
      final index = ActivityStatus.values.indexOf(entry.key);
      final color = Color(int.parse('0xFF${entry.key.colorHex.substring(1)}'));
      
      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: entry.value.toDouble(),
            color: color,
            width: 20,
            borderRadius: BorderRadius.circular(4),
          ),
        ],
      );
    }).toList();

    return BarChart(
      BarChartData(
        gridData: FlGridData(show: true),
        titlesData: FlTitlesData(
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                );
              },
            ),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 50,
              getTitlesWidget: (value, meta) {
                if (value.toInt() >= 0 && value.toInt() < ActivityStatus.values.length) {
                  return Padding(
                    padding: EdgeInsets.only(top: 8),
                    child: Text(
                      ActivityStatus.values[value.toInt()].displayName,
                      style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                      textAlign: TextAlign.center,
                    ),
                  );
                }
                return Text('');
              },
            ),
          ),
          rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: true),
        barGroups: barGroups,
      ),
    );
  }

  /// 获取底部标题
  String _getBottomTitle(int index) {
    if (index >= _healthData.length) return '';
    
    final data = _healthData[index];
    switch (_selectedPeriod) {
      case TimePeriod.day:
        return '${data.datetime.hour}:${data.datetime.minute.toString().padLeft(2, '0')}';
      case TimePeriod.week:
        return '${data.datetime.month}/${data.datetime.day}';
      case TimePeriod.month:
        return '${data.datetime.day}日';
    }
  }

  /// 构建统计信息
  Widget _buildStatisticsInfo() {
    // 统计各活动状态的出现次数和时长
    final statusCounts = <ActivityStatus, int>{};
    for (final status in ActivityStatus.values) {
      statusCounts[status] = _healthData.where((data) => data.activityStatus == status).length;
    }

    final mostActiveStatus = statusCounts.entries.reduce((a, b) => a.value > b.value ? a : b);
    final totalRecords = _healthData.length;

    return Container(
      padding: EdgeInsets.all(16),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem('最常见状态', mostActiveStatus.key.displayName, 
                      Color(int.parse('0xFF${mostActiveStatus.key.colorHex.substring(1)}'))),
                  _buildStatItem('出现次数', '${mostActiveStatus.value}', Colors.blue),
                  _buildStatItem('占比', '${(mostActiveStatus.value / totalRecords * 100).toStringAsFixed(1)}%', Colors.green),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建统计项
  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
        SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }
}
