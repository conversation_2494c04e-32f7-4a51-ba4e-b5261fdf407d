name: pet_care
description: A Flutter application for pet care management.

publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: ^3.5.3
  # sdk: ">=2.17.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter

  # 状态管理
  provider: ^6.0.5

  # 网络请求
  http: ^1.2.2

  # 蓝牙通信
  flutter_blue_plus: ^1.33.5

  # 权限管理
  permission_handler: ^10.0.0

  # 日志记录
  logger: ^2.4.0

  # 堆栈跟踪
  stack_trace: ^1.11.0 

  # 本地存储
  shared_preferences: ^2.3.3
  # 本地安全存储
  flutter_secure_storage: ^9.2.4

  # JSON 序列化，开发依赖需要自动序列化工具
  json_annotation: ^4.8.0

  # 二维码生成
  qr_flutter: ^4.0.0

  # 路径管理
  path_provider: ^2.0.11

  # 声音播放和录制
  flutter_sound: ^9.6.0
  # 声音播放
  audioplayers: ^6.1.0
  # 声音波形  
  audio_waveforms: ^1.3.0

  # 国际化和本地化
  flutter_localizations:
    sdk: flutter
  intl: ^0.19.0

  cupertino_icons: ^1.0.8

  connectivity_plus: ^6.0.0

  # ESP32-S2 BluFi协议
  esp_blufi: ^0.1.2

  # 震动
  vibration: ^3.1.0
  # 根据需要添加其他依赖...

  custom_refresh_indicator: ^2.0.1
  
  #加密算法
  crypto: ^3.0.3

  # wifi扫描
  wifi_scan: ^0.4.1+2

  # jwt解码
  jwt_decoder: ^2.0.1

  # 二维码扫描
  mobile_scanner: ^6.0.10

  # 网络信息
  network_info_plus: ^6.1.4
  
  # 通知服务
  flutter_local_notifications: ^19.2.1
  fl_chart: ^0.71.0


# 开发依赖
dev_dependencies:
  flutter_test:
    sdk: flutter
  # 自动序列化工具
  build_runner: ^2.3.3
  json_serializable: ^6.6.1

flutter:
  uses-material-design: true

  # 资源文件
  assets:
    - assets/images/
    - assets/audios/
    - assets/alarm/