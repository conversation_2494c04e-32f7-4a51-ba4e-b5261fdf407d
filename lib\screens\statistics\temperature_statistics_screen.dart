import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../widgets/time_period_selector.dart';
import '../../services/mock_data_service.dart';
import '../../models/extended_health_data.dart';

/// 体温统计界面
class TemperatureStatisticsScreen extends StatefulWidget {
  @override
  _TemperatureStatisticsScreenState createState() => _TemperatureStatisticsScreenState();
}

class _TemperatureStatisticsScreenState extends State<TemperatureStatisticsScreen> {
  TimePeriod _selectedPeriod = TimePeriod.day;
  DateTime _currentDate = DateTime.now();
  List<ExtendedHealthData> _healthData = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// 加载数据
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      List<ExtendedHealthData> data;
      switch (_selectedPeriod) {
        case TimePeriod.day:
          data = MockDataService.generateDayData(date: _currentDate);
          break;
        case TimePeriod.week:
          data = MockDataService.generateWeekData(startDate: _currentDate);
          break;
        case TimePeriod.month:
          data = MockDataService.generateMonthData(month: _currentDate);
          break;
      }
      
      setState(() {
        _healthData = data;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('加载数据失败: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('体温统计'),
        backgroundColor: Colors.red.shade50,
      ),
      body: Column(
        children: [
          // 时间段选择器
          TimePeriodSelectorWithNavigation(
            selectedPeriod: _selectedPeriod,
            onPeriodChanged: (period) {
              setState(() {
                _selectedPeriod = period;
              });
              _loadData();
            },
            currentDate: _currentDate,
            onDateChanged: (date) {
              setState(() {
                _currentDate = date;
              });
              _loadData();
            },
          ),
          
          // 图表区域
          Expanded(
            child: _isLoading
                ? Center(child: CircularProgressIndicator())
                : Padding(
                    padding: EdgeInsets.all(16),
                    child: Card(
                      elevation: 4,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '体温变化趋势',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                            SizedBox(height: 16),
                            Expanded(
                              child: _buildLineChart(),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
          ),
          
          // 统计信息
          if (!_isLoading) _buildStatisticsInfo(),
        ],
      ),
    );
  }

  /// 构建曲线图
  Widget _buildLineChart() {
    if (_healthData.isEmpty) {
      return Center(
        child: Text(
          '暂无数据',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade600,
          ),
        ),
      );
    }

    final spots = _healthData.asMap().entries.map((entry) {
      return FlSpot(entry.key.toDouble(), entry.value.temperature);
    }).toList();

    return LineChart(
      LineChartData(
        gridData: FlGridData(show: true),
        titlesData: FlTitlesData(
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 50,
              getTitlesWidget: (value, meta) {
                return Text(
                  '${value.toStringAsFixed(1)}°C',
                  style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                );
              },
            ),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              getTitlesWidget: (value, meta) {
                return Text(
                  _getBottomTitle(value.toInt()),
                  style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                );
              },
            ),
          ),
          rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: true),
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: true,
            color: _getTemperatureColor(),
            barWidth: 3,
            dotData: FlDotData(show: true),
            belowBarData: BarAreaData(
              show: true,
              color: _getTemperatureColor().withOpacity(0.1),
            ),
          ),
        ],
        // 添加正常体温范围的参考线
        extraLinesData: ExtraLinesData(
          horizontalLines: [
            HorizontalLine(
              y: 38.0, // 正常体温上限
              color: Colors.orange.withOpacity(0.5),
              strokeWidth: 2,
              dashArray: [5, 5],
            ),
            HorizontalLine(
              y: 37.0, // 正常体温下限
              color: Colors.green.withOpacity(0.5),
              strokeWidth: 2,
              dashArray: [5, 5],
            ),
          ],
        ),
      ),
    );
  }

  /// 获取体温颜色
  Color _getTemperatureColor() {
    if (_healthData.isEmpty) return Colors.blue;
    
    final avgTemp = _healthData.fold<double>(0, (sum, data) => sum + data.temperature) / _healthData.length;
    
    if (avgTemp >= 39.0) return Colors.red;
    if (avgTemp >= 38.5) return Colors.orange;
    if (avgTemp >= 37.5) return Colors.yellow.shade700;
    return Colors.green;
  }

  /// 获取底部标题
  String _getBottomTitle(int index) {
    if (index >= _healthData.length) return '';
    
    final data = _healthData[index];
    switch (_selectedPeriod) {
      case TimePeriod.day:
        return '${data.datetime.hour}:${data.datetime.minute.toString().padLeft(2, '0')}';
      case TimePeriod.week:
        return '${data.datetime.month}/${data.datetime.day}';
      case TimePeriod.month:
        return '${data.datetime.day}日';
    }
  }

  /// 构建统计信息
  Widget _buildStatisticsInfo() {
    final temperatures = _healthData.map((d) => d.temperature).toList();
    final avgTemp = temperatures.fold<double>(0, (sum, temp) => sum + temp) / temperatures.length;
    final maxTemp = temperatures.reduce((a, b) => a > b ? a : b);
    final minTemp = temperatures.reduce((a, b) => a < b ? a : b);
    
    // 计算异常体温次数
    final abnormalCount = temperatures.where((temp) => temp > 38.0 || temp < 37.0).length;

    return Container(
      padding: EdgeInsets.all(16),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem('平均体温', '${avgTemp.toStringAsFixed(1)}°C', _getTemperatureColor()),
                  _buildStatItem('最高', '${maxTemp.toStringAsFixed(1)}°C', Colors.red),
                  _buildStatItem('最低', '${minTemp.toStringAsFixed(1)}°C', Colors.blue),
                ],
              ),
              SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem('异常次数', abnormalCount.toString(), Colors.orange),
                  _buildStatItem('正常范围', '37.0-38.0°C', Colors.green),
                  _buildStatItem('测量次数', temperatures.length.toString(), Colors.grey),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建统计项
  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
        SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }
}
