import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../widgets/time_period_selector.dart';
import '../../services/mock_data_service.dart';
import '../../models/extended_health_data.dart';

/// 步数统计界面
class StepStatisticsScreen extends StatefulWidget {
  @override
  _StepStatisticsScreenState createState() => _StepStatisticsScreenState();
}

class _StepStatisticsScreenState extends State<StepStatisticsScreen> {
  TimePeriod _selectedPeriod = TimePeriod.day;
  DateTime _currentDate = DateTime.now();
  List<ExtendedHealthData> _healthData = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// 加载数据
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      List<ExtendedHealthData> data;
      switch (_selectedPeriod) {
        case TimePeriod.day:
          data = MockDataService.generateDayData(date: _currentDate);
          break;
        case TimePeriod.week:
          data = MockDataService.generateWeekData(startDate: _currentDate);
          break;
        case TimePeriod.month:
          data = MockDataService.generateMonthData(month: _currentDate);
          break;
      }
      
      setState(() {
        _healthData = data;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('加载数据失败: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('步数统计'),
        backgroundColor: Colors.blue.shade50,
      ),
      body: Column(
        children: [
          // 时间段选择器
          TimePeriodSelectorWithNavigation(
            selectedPeriod: _selectedPeriod,
            onPeriodChanged: (period) {
              setState(() {
                _selectedPeriod = period;
              });
              _loadData();
            },
            currentDate: _currentDate,
            onDateChanged: (date) {
              setState(() {
                _currentDate = date;
              });
              _loadData();
            },
          ),

          // 图表区域 - 占满整个屏幕宽度
          Expanded(
            child: _isLoading
                ? Center(child: CircularProgressIndicator())
                : Container(
                    width: double.infinity,
                    margin: EdgeInsets.symmetric(horizontal: 8, vertical: 16),
                    child: Card(
                      elevation: 4,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '步数趋势',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                            SizedBox(height: 16),
                            Expanded(
                              child: _buildChart(),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
          ),
          
          // 统计信息
          if (!_isLoading) _buildStatisticsInfo(),
        ],
      ),
    );
  }

  /// 构建图表
  Widget _buildChart() {
    if (_healthData.isEmpty) {
      return Center(
        child: Text(
          '暂无数据',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade600,
          ),
        ),
      );
    }

    // 根据时间段自动选择图表类型
    // 日模式和月模式显示曲线图，周模式显示柱状图
    switch (_selectedPeriod) {
      case TimePeriod.week:
        return _buildBarChart();
      case TimePeriod.day:
      case TimePeriod.month:
      default:
        return _buildLineChart();
    }
  }

  /// 构建曲线图
  Widget _buildLineChart() {
    final spots = _healthData.asMap().entries.map((entry) {
      return FlSpot(entry.key.toDouble(), entry.value.stepCount.toDouble());
    }).toList();

    return AspectRatio(
      aspectRatio: 2.0, // 设置宽高比为2:1
      child: LineChart(
        LineChartData(
          gridData: FlGridData(
            show: true,
            drawVerticalLine: true,
            drawHorizontalLine: true,
            horizontalInterval: null,
            verticalInterval: null,
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color: Colors.grey.shade300,
                strokeWidth: 1,
                dashArray: [5, 5], // 设置虚线样式
              );
            },
            getDrawingVerticalLine: (value) {
              return FlLine(
                color: Colors.grey.shade300,
                strokeWidth: 1,
                dashArray: [5, 5], // 设置虚线样式
              );
            },
          ),
          titlesData: FlTitlesData(
            leftTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)), // 隐藏左侧标题
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 40, // 增加底部预留空间，拉开与绘图区域的距离
                getTitlesWidget: (value, meta) {
                  return Padding(
                    padding: const EdgeInsets.only(top: 8.0), // 向下推进横坐标值
                    child: Text(
                      _getBottomTitle(value.toInt()),
                      style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                    ),
                  );
                },
              ),
            ),
            rightTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 40, // 减少右侧预留空间，让数值更靠近右边缘
                getTitlesWidget: (value, meta) {
                  return Padding(
                    padding: const EdgeInsets.only(left: 8.0), // 向右推进数值
                    child: Text(
                      value.toInt().toString(),
                      style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                      textAlign: TextAlign.right,
                    ),
                  );
                },
              ),
            ), // 显示右侧步数标题
            topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          ),
          borderData: FlBorderData(show: false), // 去掉坐标轴外框
          lineTouchData: LineTouchData(
            enabled: true,
            touchTooltipData: LineTouchTooltipData(
              getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
                return touchedBarSpots.map((barSpot) {
                  final index = barSpot.x.toInt();
                  String timeRange = '';

                  if (_selectedPeriod == TimePeriod.day) {
                    // 日模式显示时间段
                    final hour = index ~/ 4;
                    final minute = (index % 4) * 15;
                    final nextMinute = minute + 15;
                    if (nextMinute == 60) {
                      timeRange = '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}~${(hour + 1).toString().padLeft(2, '0')}:00';
                    } else {
                      timeRange = '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}~${hour.toString().padLeft(2, '0')}:${nextMinute.toString().padLeft(2, '0')}';
                    }
                  } else if (_selectedPeriod == TimePeriod.month) {
                    // 月模式显示具体日期
                    final day = index + 1;
                    final month = DateTime.now().month; // 使用当前月份
                    timeRange = '${month}月${day}日';
                  } else {
                    // 周模式显示星期
                    final weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
                    timeRange = weekdays[index % 7];
                  }

                  return LineTooltipItem(
                    '$timeRange\n${barSpot.y.toInt()}步',
                    const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  );
                }).toList();
              },
            ),
          ),
          lineBarsData: [
            LineChartBarData(
              spots: spots,
              isCurved: true, // 使用光滑曲线
              curveSmoothness: 0.35, // 设置曲线平滑度，值越大越平滑
              color: Colors.blue,
              barWidth: 3,
              dotData: FlDotData(
                show: true,
                getDotPainter: (spot, percent, barData, index) {
                  return FlDotCirclePainter(
                    radius: 4,
                    color: Colors.blue,
                    strokeWidth: 2,
                    strokeColor: Colors.white,
                  );
                },
              ),
              belowBarData: BarAreaData(
                show: true,
                color: Colors.blue.withOpacity(0.1),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建柱状图
  Widget _buildBarChart() {
    final barGroups = _healthData.asMap().entries.map((entry) {
      return BarChartGroupData(
        x: entry.key,
        barRods: [
          BarChartRodData(
            toY: entry.value.stepCount.toDouble(),
            color: Colors.blue,
            width: 16,
            borderRadius: BorderRadius.circular(4),
          ),
        ],
      );
    }).toList();

    return AspectRatio(
      aspectRatio: 2.0, // 设置宽高比为2:1
      child: BarChart(
        BarChartData(
          gridData: FlGridData(
            show: true,
            drawVerticalLine: true,
            drawHorizontalLine: true,
            horizontalInterval: null,
            verticalInterval: null,
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color: Colors.grey.shade300,
                strokeWidth: 1,
                dashArray: [5, 5], // 设置虚线样式
              );
            },
            getDrawingVerticalLine: (value) {
              return FlLine(
                color: Colors.grey.shade300,
                strokeWidth: 1,
                dashArray: [5, 5], // 设置虚线样式
              );
            },
          ),
          titlesData: FlTitlesData(
            leftTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)), // 隐藏左侧标题
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 30,
                getTitlesWidget: (value, meta) {
                  return Text(
                    _getBottomTitle(value.toInt()),
                    style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                  );
                },
              ),
            ),
            rightTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 40, // 减少右侧预留空间，让数值更靠近右边缘
                getTitlesWidget: (value, meta) {
                  return Padding(
                    padding: const EdgeInsets.only(left: 8.0), // 向右推进数值
                    child: Text(
                      value.toInt().toString(),
                      style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                      textAlign: TextAlign.right,
                    ),
                  );
                },
              ),
            ), // 显示右侧步数标题
            topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          ),
          borderData: FlBorderData(show: false), // 去掉坐标轴外框
          barTouchData: BarTouchData(
            enabled: true,
            touchTooltipData: BarTouchTooltipData(
              getTooltipItem: (group, groupIndex, rod, rodIndex) {
                String timeRange = '';

                if (_selectedPeriod == TimePeriod.week) {
                  // 周模式显示星期
                  final weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
                  timeRange = weekdays[groupIndex % 7];
                }

                return BarTooltipItem(
                  '$timeRange\n${rod.toY.toInt()}步',
                  const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                );
              },
            ),
          ),
          barGroups: barGroups,
        ),
      ),
    );
  }

  /// 获取底部标题
  String _getBottomTitle(int index) {
    if (index >= _healthData.length) return '';
    
    final data = _healthData[index];
    switch (_selectedPeriod) {
      case TimePeriod.day:
        return '${data.datetime.hour}:${data.datetime.minute.toString().padLeft(2, '0')}';
      case TimePeriod.week:
        return '${data.datetime.month}/${data.datetime.day}';
      case TimePeriod.month:
        return '${data.datetime.day}日';
    }
  }

  /// 构建统计信息
  Widget _buildStatisticsInfo() {
    final totalSteps = _healthData.fold<int>(0, (sum, data) => sum + data.stepCount);
    final avgSteps = _healthData.isNotEmpty ? totalSteps / _healthData.length : 0;
    final maxSteps = _healthData.isNotEmpty 
        ? _healthData.map((d) => d.stepCount).reduce((a, b) => a > b ? a : b)
        : 0;

    return Container(
      padding: EdgeInsets.all(16),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem('总步数', totalSteps.toString(), Colors.blue),
              _buildStatItem('平均', avgSteps.toStringAsFixed(0), Colors.green),
              _buildStatItem('最高', maxSteps.toString(), Colors.orange),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建统计项
  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
        SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }
}
