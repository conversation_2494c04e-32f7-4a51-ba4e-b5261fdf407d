import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../widgets/time_period_selector.dart';
import '../../widgets/chart_view_selector.dart';
import '../../services/mock_data_service.dart';
import '../../models/extended_health_data.dart';
import '../../models/health_detection.dart';

/// 健康检测统计界面
class HealthDetectionStatisticsScreen extends StatefulWidget {
  @override
  _HealthDetectionStatisticsScreenState createState() => _HealthDetectionStatisticsScreenState();
}

class _HealthDetectionStatisticsScreenState extends State<HealthDetectionStatisticsScreen> {
  TimePeriod _selectedPeriod = TimePeriod.day;
  ChartViewType _selectedChartType = ChartViewType.scatter;
  DateTime _currentDate = DateTime.now();
  List<ExtendedHealthData> _healthData = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// 加载数据
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      List<ExtendedHealthData> data;
      switch (_selectedPeriod) {
        case TimePeriod.day:
          data = MockDataService.generateDayData(date: _currentDate);
          break;
        case TimePeriod.week:
          data = MockDataService.generateWeekData(startDate: _currentDate);
          break;
        case TimePeriod.month:
          data = MockDataService.generateMonthData(month: _currentDate);
          break;
      }
      
      setState(() {
        _healthData = data;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('加载数据失败: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('健康检测统计'),
        backgroundColor: Colors.red.shade50,
      ),
      body: Column(
        children: [
          // 时间段选择器
          TimePeriodSelectorWithNavigation(
            selectedPeriod: _selectedPeriod,
            onPeriodChanged: (period) {
              setState(() {
                _selectedPeriod = period;
              });
              _loadData();
            },
            currentDate: _currentDate,
            onDateChanged: (date) {
              setState(() {
                _currentDate = date;
              });
              _loadData();
            },
          ),
          
          // 图表类型选择器
          ChartViewSelector(
            availableTypes: ChartTypePresets.healthDetectionStatistics,
            selectedType: _selectedChartType,
            onTypeChanged: (type) {
              setState(() {
                _selectedChartType = type;
              });
            },
          ),
          
          // 图表区域
          Expanded(
            child: _isLoading
                ? Center(child: CircularProgressIndicator())
                : Padding(
                    padding: EdgeInsets.all(16),
                    child: Card(
                      elevation: 4,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _selectedChartType == ChartViewType.scatter ? '健康异常检测分布' : '健康检测统计',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                            SizedBox(height: 16),
                            Expanded(
                              child: _buildChart(),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
          ),
          
          // 统计信息
          if (!_isLoading) _buildStatisticsInfo(),
        ],
      ),
    );
  }

  /// 构建图表
  Widget _buildChart() {
    if (_healthData.isEmpty) {
      return Center(
        child: Text(
          '暂无数据',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade600,
          ),
        ),
      );
    }

    switch (_selectedChartType) {
      case ChartViewType.scatter:
        return _buildScatterChart();
      case ChartViewType.horizontalBar:
        return _buildHorizontalBarChart();
      default:
        return _buildScatterChart();
    }
  }

  /// 构建散点图（健康检测的严重程度分布）
  Widget _buildScatterChart() {
    final scatterSpots = <ScatterSpot>[];
    
    for (int i = 0; i < _healthData.length; i++) {
      final data = _healthData[i];
      for (final detection in data.healthDetections) {
        scatterSpots.add(
          ScatterSpot(
            i.toDouble(),
            detection.severityLevel.toDouble(),
            dotPainter: FlDotCirclePainter(
              radius: detection.isDangerous ? 8 : 6,
              color: detection.isDangerous ? Colors.red : Colors.orange,
            ),
          ),
        );
      }
    }

    if (scatterSpots.isEmpty) {
      return Center(
        child: Text(
          '该时间段内无健康异常检测',
          style: TextStyle(
            fontSize: 16,
            color: Colors.green.shade600,
          ),
        ),
      );
    }

    return Column(
      children: [
        // 图例
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildLegendItem('一般异常', Colors.orange),
            SizedBox(width: 20),
            _buildLegendItem('危险异常', Colors.red),
          ],
        ),
        SizedBox(height: 16),
        Expanded(
          child: ScatterChart(
            ScatterChartData(
              gridData: FlGridData(show: true),
              titlesData: FlTitlesData(
                leftTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    reservedSize: 40,
                    getTitlesWidget: (value, meta) {
                      return Text(
                        '严重度${value.toInt()}',
                        style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                      );
                    },
                  ),
                ),
                bottomTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    reservedSize: 30,
                    getTitlesWidget: (value, meta) {
                      return Text(
                        _getBottomTitle(value.toInt()),
                        style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                      );
                    },
                  ),
                ),
                rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
              ),
              borderData: FlBorderData(show: true),
              scatterSpots: scatterSpots,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建条形图（各类健康检测的出现次数）
  Widget _buildHorizontalBarChart() {
    // 统计各类健康检测的出现次数
    final detectionCounts = <HealthDetection, int>{};
    for (final data in _healthData) {
      for (final detection in data.healthDetections) {
        detectionCounts[detection] = (detectionCounts[detection] ?? 0) + 1;
      }
    }

    if (detectionCounts.isEmpty) {
      return Center(
        child: Text(
          '该时间段内无健康异常检测',
          style: TextStyle(
            fontSize: 16,
            color: Colors.green.shade600,
          ),
        ),
      );
    }

    final sortedEntries = detectionCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    final barGroups = sortedEntries.asMap().entries.map((entry) {
      final detection = entry.value.key;
      final count = entry.value.value;
      final color = detection.isDangerous ? Colors.red : Colors.orange;
      
      return BarChartGroupData(
        x: entry.key,
        barRods: [
          BarChartRodData(
            toY: count.toDouble(),
            color: color,
            width: 20,
            borderRadius: BorderRadius.circular(4),
          ),
        ],
      );
    }).toList();

    return BarChart(
      BarChartData(
        gridData: FlGridData(show: true),
        titlesData: FlTitlesData(
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                );
              },
            ),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 60,
              getTitlesWidget: (value, meta) {
                if (value.toInt() >= 0 && value.toInt() < sortedEntries.length) {
                  return Padding(
                    padding: EdgeInsets.only(top: 8),
                    child: Text(
                      sortedEntries[value.toInt()].key.displayName,
                      style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                      textAlign: TextAlign.center,
                    ),
                  );
                }
                return Text('');
              },
            ),
          ),
          rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: true),
        barGroups: barGroups,
      ),
    );
  }

  /// 构建图例项
  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        SizedBox(width: 4),
        Text(
          label,
          style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
        ),
      ],
    );
  }

  /// 获取底部标题
  String _getBottomTitle(int index) {
    if (index >= _healthData.length) return '';
    
    final data = _healthData[index];
    switch (_selectedPeriod) {
      case TimePeriod.day:
        return '${data.datetime.hour}:${data.datetime.minute.toString().padLeft(2, '0')}';
      case TimePeriod.week:
        return '${data.datetime.month}/${data.datetime.day}';
      case TimePeriod.month:
        return '${data.datetime.day}日';
    }
  }

  /// 构建统计信息
  Widget _buildStatisticsInfo() {
    // 统计健康检测信息
    final allDetections = _healthData.expand((data) => data.healthDetections).toList();
    final dangerousCount = allDetections.where((d) => d.isDangerous).length;
    final totalCount = allDetections.length;
    final feverCount = allDetections.where((d) => d == HealthDetection.fever).length;
    
    final mostCommonDetection = _getMostCommonDetection(allDetections);

    return Container(
      padding: EdgeInsets.all(16),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem('总检测数', totalCount.toString(), Colors.blue),
                  _buildStatItem('危险异常', dangerousCount.toString(), Colors.red),
                  _buildStatItem('发热次数', feverCount.toString(), Colors.orange),
                ],
              ),
              SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem('最常见', mostCommonDetection?.displayName ?? '无', Colors.purple),
                  _buildStatItem('健康状态', _getHealthStatus(dangerousCount, totalCount), _getHealthStatusColor(dangerousCount, totalCount)),
                  _buildStatItem('监测天数', '${_selectedPeriod.displayName}', Colors.grey),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 获取最常见的健康检测
  HealthDetection? _getMostCommonDetection(List<HealthDetection> detections) {
    if (detections.isEmpty) return null;
    
    final detectionCounts = <HealthDetection, int>{};
    for (final detection in detections) {
      detectionCounts[detection] = (detectionCounts[detection] ?? 0) + 1;
    }
    
    return detectionCounts.entries.reduce((a, b) => a.value > b.value ? a : b).key;
  }

  /// 获取健康状态描述
  String _getHealthStatus(int dangerousCount, int totalCount) {
    if (totalCount == 0) return '良好';
    
    final dangerousRatio = dangerousCount / totalCount;
    if (dangerousRatio == 0) return '良好';
    if (dangerousRatio < 0.1) return '注意';
    if (dangerousRatio < 0.3) return '警告';
    return '危险';
  }

  /// 获取健康状态颜色
  Color _getHealthStatusColor(int dangerousCount, int totalCount) {
    if (totalCount == 0) return Colors.green;
    
    final dangerousRatio = dangerousCount / totalCount;
    if (dangerousRatio == 0) return Colors.green;
    if (dangerousRatio < 0.1) return Colors.yellow.shade700;
    if (dangerousRatio < 0.3) return Colors.orange;
    return Colors.red;
  }

  /// 构建统计项
  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
        SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }
}
